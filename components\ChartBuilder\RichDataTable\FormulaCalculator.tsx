'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Calculator, AlertCircle, Copy, Trash2 } from "lucide-react"
import { toast } from "sonner"
import * as math from 'mathjs'
import { FormulaResult, FormulaHistory, FormulaFunction, CellReference } from './types'

interface FormulaCalculatorProps {
  data: any[]
  columns: string[]
  onClose?: () => void
}

// Comprehensive Excel-like functions
const FORMULA_FUNCTIONS: FormulaFunction[] = [
  // Mathematical Functions
  {
    name: '<PERSON><PERSON>',
    description: 'Adds all numbers in a range',
    syntax: 'SUM(column_name)',
    example: 'SUM(salary)',
    category: 'math'
  },
  {
    name: 'PRODUCT',
    description: 'Multiplies all numbers in a range',
    syntax: 'PRODUCT(column_name)',
    example: 'PRODUCT(quantity)',
    category: 'math'
  },
  {
    name: 'POWER',
    description: 'Returns a number raised to a power',
    syntax: 'POWER(number, power)',
    example: 'POWER(2, 3)',
    category: 'math'
  },
  {
    name: 'SQRT',
    description: 'Returns the square root of a number',
    syntax: 'SQRT(number)',
    example: 'SQRT(16)',
    category: 'math'
  },
  {
    name: 'ABS',
    description: 'Returns the absolute value of a number',
    syntax: 'ABS(number)',
    example: 'ABS(-5)',
    category: 'math'
  },
  {
    name: 'ROUND',
    description: 'Rounds a number to specified decimals',
    syntax: 'ROUND(number, decimals)',
    example: 'ROUND(3.14159, 2)',
    category: 'math'
  },
  {
    name: 'CEILING',
    description: 'Rounds a number up to the nearest integer',
    syntax: 'CEILING(number)',
    example: 'CEILING(4.2)',
    category: 'math'
  },
  {
    name: 'FLOOR',
    description: 'Rounds a number down to the nearest integer',
    syntax: 'FLOOR(number)',
    example: 'FLOOR(4.8)',
    category: 'math'
  },
  {
    name: 'MOD',
    description: 'Returns the remainder after division',
    syntax: 'MOD(number, divisor)',
    example: 'MOD(10, 3)',
    category: 'math'
  },

  // Statistical Functions
  {
    name: 'AVERAGE',
    description: 'Calculates the average of numbers',
    syntax: 'AVERAGE(column_name)',
    example: 'AVERAGE(age)',
    category: 'statistical'
  },
  {
    name: 'MEDIAN',
    description: 'Returns the median of numbers',
    syntax: 'MEDIAN(column_name)',
    example: 'MEDIAN(salary)',
    category: 'statistical'
  },
  {
    name: 'MODE',
    description: 'Returns the most frequently occurring value',
    syntax: 'MODE(column_name)',
    example: 'MODE(department)',
    category: 'statistical'
  },
  {
    name: 'COUNT',
    description: 'Counts non-empty cells',
    syntax: 'COUNT(column_name)',
    example: 'COUNT(employee_id)',
    category: 'statistical'
  },
  {
    name: 'COUNTA',
    description: 'Counts non-empty cells including text',
    syntax: 'COUNTA(column_name)',
    example: 'COUNTA(name)',
    category: 'statistical'
  },
  {
    name: 'COUNTIF',
    description: 'Counts cells that meet criteria',
    syntax: 'COUNTIF(column_name, criteria)',
    example: 'COUNTIF(department, "IT")',
    category: 'statistical'
  },
  {
    name: 'MIN',
    description: 'Finds the minimum value',
    syntax: 'MIN(column_name)',
    example: 'MIN(salary)',
    category: 'statistical'
  },
  {
    name: 'MAX',
    description: 'Finds the maximum value',
    syntax: 'MAX(column_name)',
    example: 'MAX(salary)',
    category: 'statistical'
  },
  {
    name: 'STDEV',
    description: 'Calculates standard deviation',
    syntax: 'STDEV(column_name)',
    example: 'STDEV(salary)',
    category: 'statistical'
  },
  {
    name: 'VAR',
    description: 'Calculates variance',
    syntax: 'VAR(column_name)',
    example: 'VAR(age)',
    category: 'statistical'
  },

  // Logical Functions
  {
    name: 'IF',
    description: 'Returns one value if condition is true, another if false',
    syntax: 'IF(condition, true_value, false_value)',
    example: 'IF(AVERAGE(salary) > 50000, "High", "Low")',
    category: 'logical'
  },
  {
    name: 'AND',
    description: 'Returns true if all conditions are true',
    syntax: 'AND(condition1, condition2)',
    example: 'AND(MIN(age) >= 18, MAX(age) <= 65)',
    category: 'logical'
  },
  {
    name: 'OR',
    description: 'Returns true if any condition is true',
    syntax: 'OR(condition1, condition2)',
    example: 'OR(SUM(salary) > 100000, COUNT(employee_id) > 10)',
    category: 'logical'
  },
  {
    name: 'NOT',
    description: 'Reverses the logic of its argument',
    syntax: 'NOT(condition)',
    example: 'NOT(AVERAGE(age) < 30)',
    category: 'logical'
  },

  // Text Functions
  {
    name: 'CONCATENATE',
    description: 'Joins text strings together',
    syntax: 'CONCATENATE(text1, text2)',
    example: 'CONCATENATE("Total: ", SUM(salary))',
    category: 'text'
  },
  {
    name: 'LEN',
    description: 'Returns the length of text',
    syntax: 'LEN(text)',
    example: 'LEN("Hello World")',
    category: 'text'
  },
  {
    name: 'UPPER',
    description: 'Converts text to uppercase',
    syntax: 'UPPER(text)',
    example: 'UPPER("hello")',
    category: 'text'
  },
  {
    name: 'LOWER',
    description: 'Converts text to lowercase',
    syntax: 'LOWER(text)',
    example: 'LOWER("HELLO")',
    category: 'text'
  },

  // Date Functions
  {
    name: 'TODAY',
    description: 'Returns today\'s date',
    syntax: 'TODAY()',
    example: 'TODAY()',
    category: 'date'
  },
  {
    name: 'NOW',
    description: 'Returns current date and time',
    syntax: 'NOW()',
    example: 'NOW()',
    category: 'date'
  },
  {
    name: 'YEAR',
    description: 'Extracts year from date',
    syntax: 'YEAR(date)',
    example: 'YEAR(TODAY())',
    category: 'date'
  },
  {
    name: 'MONTH',
    description: 'Extracts month from date',
    syntax: 'MONTH(date)',
    example: 'MONTH(TODAY())',
    category: 'date'
  },
  {
    name: 'DAY',
    description: 'Extracts day from date',
    syntax: 'DAY(date)',
    example: 'DAY(TODAY())',
    category: 'date'
  }
]

export function FormulaCalculator({ data, columns, onClose }: FormulaCalculatorProps) {
  const [formula, setFormula] = useState('')
  const [result, setResult] = useState<FormulaResult | null>(null)
  const [history, setHistory] = useState<FormulaHistory[]>([])
  const [selectedFunction, setSelectedFunction] = useState<FormulaFunction | null>(null)

  // Calculate comprehensive column statistics for reference
  const columnStats = useMemo(() => {
    const stats: Record<string, any> = {}

    columns.forEach(column => {
      const values = data.map(row => row[column]).filter(val => val !== null && val !== undefined)
      const numericValues = values.filter(val => typeof val === 'number' || !isNaN(Number(val))).map(Number)
      const textValues = values.filter(val => typeof val === 'string')

      // Calculate median
      const sortedNumeric = [...numericValues].sort((a, b) => a - b)
      const median = sortedNumeric.length > 0
        ? sortedNumeric.length % 2 === 0
          ? (sortedNumeric[sortedNumeric.length / 2 - 1] + sortedNumeric[sortedNumeric.length / 2]) / 2
          : sortedNumeric[Math.floor(sortedNumeric.length / 2)]
        : 0

      // Calculate mode
      const frequency: Record<string, number> = {}
      values.forEach(val => {
        const key = String(val)
        frequency[key] = (frequency[key] || 0) + 1
      })
      const mode = Object.keys(frequency).reduce((a, b) => frequency[a] > frequency[b] ? a : b, '')

      // Calculate standard deviation and variance
      const mean = numericValues.length > 0 ? numericValues.reduce((a, b) => a + b, 0) / numericValues.length : 0
      const variance = numericValues.length > 0
        ? numericValues.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / numericValues.length
        : 0
      const stdev = Math.sqrt(variance)

      // Calculate product
      const product = numericValues.length > 0 ? numericValues.reduce((a, b) => a * b, 1) : 0

      stats[column] = {
        count: values.length,
        counta: values.length, // Same as count for non-empty values
        numericCount: numericValues.length,
        textCount: textValues.length,
        sum: numericValues.length > 0 ? numericValues.reduce((a, b) => a + b, 0) : 0,
        product,
        avg: mean,
        median,
        mode,
        min: numericValues.length > 0 ? Math.min(...numericValues) : 0,
        max: numericValues.length > 0 ? Math.max(...numericValues) : 0,
        stdev,
        variance,
        type: numericValues.length > 0 ? 'number' : 'text',
        values: numericValues,
        allValues: values
      }
    })

    return stats
  }, [data, columns])

  // Enhanced formula evaluation with comprehensive Excel-like functions
  const evaluateFormula = useCallback((formulaText: string): FormulaResult => {
    try {
      let processedFormula = formulaText.trim()

      // Helper function to count values matching criteria
      const countIf = (column: string, criteria: string) => {
        if (!columnStats[column]) return 0
        const cleanCriteria = criteria.replace(/['"]/g, '')
        return columnStats[column].allValues.filter((val: any) =>
          String(val).toLowerCase() === cleanCriteria.toLowerCase()
        ).length
      }

      // Replace Excel-like functions with calculated values
      processedFormula = processedFormula
        // Mathematical Functions
        .replace(/SUM\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return columnStats[column].sum.toString()
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/PRODUCT\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return columnStats[column].product.toString()
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/POWER\(([^,]+),\s*([^)]+)\)/gi, (match, base, exp) => {
          return `pow(${base}, ${exp})`
        })
        .replace(/SQRT\(([^)]+)\)/gi, (match, num) => {
          return `sqrt(${num})`
        })
        .replace(/ABS\(([^)]+)\)/gi, (match, num) => {
          return `abs(${num})`
        })
        .replace(/CEILING\(([^)]+)\)/gi, (match, num) => {
          return `ceil(${num})`
        })
        .replace(/FLOOR\(([^)]+)\)/gi, (match, num) => {
          return `floor(${num})`
        })
        .replace(/MOD\(([^,]+),\s*([^)]+)\)/gi, (match, num, div) => {
          return `mod(${num}, ${div})`
        })

        // Statistical Functions
        .replace(/AVERAGE\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return columnStats[column].avg.toString()
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/MEDIAN\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return columnStats[column].median.toString()
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/MODE\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return `"${columnStats[column].mode}"`
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/COUNT\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return columnStats[column].count.toString()
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/COUNTA\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return columnStats[column].counta.toString()
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/COUNTIF\((\w+),\s*([^)]+)\)/gi, (match, column, criteria) => {
          return countIf(column, criteria).toString()
        })
        .replace(/MIN\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return columnStats[column].min.toString()
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/MAX\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return columnStats[column].max.toString()
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/STDEV\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return columnStats[column].stdev.toString()
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/VAR\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) return columnStats[column].variance.toString()
          throw new Error(`Column '${column}' not found`)
        })

        // Logical Functions
        .replace(/IF\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, condition, trueVal, falseVal) => {
          return `(${condition}) ? (${trueVal}) : (${falseVal})`
        })
        .replace(/AND\(([^)]+)\)/gi, (match, conditions) => {
          const conds = conditions.split(',').map((c: string) => c.trim())
          return `(${conds.join(' && ')})`
        })
        .replace(/OR\(([^)]+)\)/gi, (match, conditions) => {
          const conds = conditions.split(',').map((c: string) => c.trim())
          return `(${conds.join(' || ')})`
        })
        .replace(/NOT\(([^)]+)\)/gi, (match, condition) => {
          return `!(${condition})`
        })

        // Text Functions
        .replace(/CONCATENATE\(([^)]+)\)/gi, (match, args) => {
          const parts = args.split(',').map(p => p.trim())
          return parts.join(' + ')
        })
        .replace(/LEN\(([^)]+)\)/gi, (match, text) => {
          return `length(${text})`
        })
        .replace(/UPPER\(([^)]+)\)/gi, (match, text) => {
          return `uppercase(${text})`
        })
        .replace(/LOWER\(([^)]+)\)/gi, (match, text) => {
          return `lowercase(${text})`
        })

        // Date Functions
        .replace(/TODAY\(\)/gi, () => {
          return new Date().toISOString().split('T')[0]
        })
        .replace(/NOW\(\)/gi, () => {
          return new Date().toISOString()
        })
        .replace(/YEAR\(([^)]+)\)/gi, (match, date) => {
          return `year(${date})`
        })
        .replace(/MONTH\(([^)]+)\)/gi, (match, date) => {
          return `month(${date})`
        })
        .replace(/DAY\(([^)]+)\)/gi, (match, date) => {
          return `day(${date})`
        })

      // Evaluate using mathjs with custom functions
      const scope = {
        // Add custom functions for text operations
        length: (str: string) => String(str).length,
        uppercase: (str: string) => String(str).toUpperCase(),
        lowercase: (str: string) => String(str).toLowerCase(),
        year: (date: string) => new Date(date).getFullYear(),
        month: (date: string) => new Date(date).getMonth() + 1,
        day: (date: string) => new Date(date).getDate()
      }

      const calculatedResult = math.evaluate(processedFormula, scope)

      return {
        formula: formulaText,
        result: calculatedResult,
        timestamp: Date.now()
      }
    } catch (error: any) {
      return {
        formula: formulaText,
        result: null,
        error: error.message || 'Invalid formula',
        timestamp: Date.now()
      }
    }
  }, [columnStats])

  // Handle formula calculation
  const handleCalculate = () => {
    if (!formula.trim()) {
      toast.error('Please enter a formula')
      return
    }

    const calculationResult = evaluateFormula(formula)
    setResult(calculationResult)

    // Add to history if successful
    if (calculationResult.result !== null) {
      const historyItem: FormulaHistory = {
        id: Date.now().toString(),
        formula: calculationResult.formula,
        result: calculationResult.result,
        timestamp: calculationResult.timestamp
      }
      setHistory(prev => [historyItem, ...prev.slice(0, 9)]) // Keep last 10 items
      toast.success('Formula calculated successfully')
    } else {
      toast.error(calculationResult.error || 'Calculation failed')
    }
  }

  // Insert function into formula
  const insertFunction = (func: FormulaFunction) => {
    const insertion = func.syntax
    setFormula(prev => prev + insertion)
    setSelectedFunction(func)
  }

  // Insert column name into formula
  const insertColumn = (column: string) => {
    setFormula(prev => prev + column)
  }

  // Copy result to clipboard
  const copyResult = () => {
    if (result?.result !== null) {
      navigator.clipboard.writeText(result.result.toString())
      toast.success('Result copied to clipboard')
    }
  }

  // Load formula from history
  const loadFromHistory = (historyItem: FormulaHistory) => {
    setFormula(historyItem.formula)
    setResult({
      formula: historyItem.formula,
      result: historyItem.result,
      timestamp: historyItem.timestamp
    })
  }

  // Clear history
  const clearHistory = () => {
    setHistory([])
    toast.success('History cleared')
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Calculator className="h-5 w-5" />
          Formula Calculator
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Formula Input */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Excel Formula</label>
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFormula('')}
                className="h-7 px-2 text-xs"
              >
                Clear
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (columns.length > 0) {
                    setFormula(`SUM(${columns[0]})`)
                  }
                }}
                className="h-7 px-2 text-xs"
              >
                Quick SUM
              </Button>
            </div>
          </div>
          <div className="flex gap-2">
            <Input
              value={formula}
              onChange={(e) => setFormula(e.target.value)}
              placeholder="Enter Excel formula (e.g., SUM(salary) + AVERAGE(bonus) * 0.1)"
              className="font-mono text-sm"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleCalculate()
                }
              }}
            />
            <Button onClick={handleCalculate} size="sm" className="px-6">
              Calculate
            </Button>
          </div>

          {/* Quick Insert Buttons */}
          <div className="flex flex-wrap gap-1">
            {['+', '-', '*', '/', '(', ')', ',', '=', '>', '<'].map((op) => (
              <Button
                key={op}
                variant="outline"
                size="sm"
                onClick={() => setFormula(prev => prev + op)}
                className="h-6 w-6 p-0 text-xs"
              >
                {op}
              </Button>
            ))}
          </div>
        </div>

        {/* Result Display */}
        {result && (
          <div className="p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">Result:</div>
                <div className="text-lg font-mono">
                  {result.error ? (
                    <span className="text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {result.error}
                    </span>
                  ) : (
                    <span className="text-green-600">
                      {typeof result.result === 'number'
                        ? result.result.toLocaleString()
                        : result.result}
                    </span>
                  )}
                </div>
              </div>
              {result.result !== null && (
                <Button variant="outline" size="sm" onClick={copyResult}>
                  <Copy className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        )}

        <Tabs defaultValue="functions" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="functions">Functions</TabsTrigger>
            <TabsTrigger value="columns">Columns</TabsTrigger>
            <TabsTrigger value="examples">Examples</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="functions" className="space-y-2">
            <div className="text-sm text-muted-foreground mb-2">
              Click any function to insert it into your formula
            </div>
            <Tabs defaultValue="math" orientation="horizontal">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="math">Math</TabsTrigger>
                <TabsTrigger value="statistical">Stats</TabsTrigger>
                <TabsTrigger value="logical">Logic</TabsTrigger>
                <TabsTrigger value="text">Text</TabsTrigger>
                <TabsTrigger value="date">Date</TabsTrigger>
              </TabsList>

              {['math', 'statistical', 'logical', 'text', 'date'].map(category => (
                <TabsContent key={category} value={category} className="mt-2">
                  <ScrollArea className="h-40">
                    <div className="space-y-2">
                      {FORMULA_FUNCTIONS.filter(func => func.category === category).map((func) => (
                        <div
                          key={func.name}
                          className="p-2 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                          onClick={() => insertFunction(func)}
                        >
                          <div className="flex items-center justify-between">
                            <Badge variant="outline" className="font-mono">{func.name}</Badge>
                            <Badge variant="secondary" className="text-xs capitalize">
                              {func.category}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground mt-1">
                            {func.description}
                          </div>
                          <div className="text-xs font-mono bg-muted/30 p-1 rounded mt-1">
                            {func.example}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>
              ))}
            </Tabs>
          </TabsContent>

          <TabsContent value="columns" className="space-y-2">
            <div className="text-sm text-muted-foreground mb-2">
              Click any column to insert it into your formula
            </div>
            <ScrollArea className="h-48">
              <div className="grid grid-cols-2 gap-2">
                {columns.map((column) => (
                  <div
                    key={column}
                    className="p-2 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => insertColumn(column)}
                  >
                    <div className="font-medium text-sm truncate" title={column}>{column}</div>
                    <div className="text-xs text-muted-foreground">
                      {columnStats[column]?.type === 'number' ? (
                        <div className="space-y-1">
                          <div>Numeric: {columnStats[column]?.numericCount}</div>
                          <div>Range: {columnStats[column]?.min} - {columnStats[column]?.max}</div>
                        </div>
                      ) : (
                        <div>Count: {columnStats[column]?.count}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="examples" className="space-y-2">
            <div className="text-sm text-muted-foreground mb-2">
              Common formula examples - click to use
            </div>
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {[
                  {
                    title: "Basic Statistics",
                    formulas: [
                      { formula: `SUM(${columns[0] || 'column'})`, description: "Sum all values in a column" },
                      { formula: `AVERAGE(${columns[0] || 'column'})`, description: "Calculate average" },
                      { formula: `COUNT(${columns[0] || 'column'})`, description: "Count non-empty values" },
                      { formula: `MAX(${columns[0] || 'column'}) - MIN(${columns[0] || 'column'})`, description: "Calculate range" }
                    ]
                  },
                  {
                    title: "Advanced Calculations",
                    formulas: [
                      { formula: `SUM(${columns[0] || 'column'}) / COUNT(${columns[0] || 'column'})`, description: "Manual average calculation" },
                      { formula: `SQRT(VAR(${columns[0] || 'column'}))`, description: "Standard deviation" },
                      { formula: `IF(AVERAGE(${columns[0] || 'column'}) > 50, "High", "Low")`, description: "Conditional result" },
                      { formula: `ROUND(AVERAGE(${columns[0] || 'column'}), 2)`, description: "Rounded average" }
                    ]
                  },
                  {
                    title: "Text & Logic",
                    formulas: [
                      { formula: `CONCATENATE("Total: ", SUM(${columns[0] || 'column'}))`, description: "Combine text and numbers" },
                      { formula: `AND(MIN(${columns[0] || 'column'}) > 0, MAX(${columns[0] || 'column'}) < 100)`, description: "Multiple conditions" },
                      { formula: `COUNTIF(${columns[0] || 'column'}, "value")`, description: "Count specific values" },
                      { formula: `NOT(AVERAGE(${columns[0] || 'column'}) < 10)`, description: "Logical negation" }
                    ]
                  }
                ].map((section, sectionIndex) => (
                  <div key={sectionIndex} className="space-y-2">
                    <h4 className="font-medium text-sm">{section.title}</h4>
                    {section.formulas.map((example, index) => (
                      <div
                        key={index}
                        className="p-2 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => setFormula(example.formula)}
                      >
                        <div className="font-mono text-sm text-blue-600 dark:text-blue-400">
                          {example.formula}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {example.description}
                        </div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="history" className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Recent Calculations</span>
              {history.length > 0 && (
                <Button variant="outline" size="sm" onClick={clearHistory}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {history.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    No calculations yet
                  </div>
                ) : (
                  history.map((item) => (
                    <div
                      key={item.id}
                      className="p-2 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                      onClick={() => loadFromHistory(item)}
                    >
                      <div className="font-mono text-sm">{item.formula}</div>
                      <div className="text-sm text-green-600">
                        = {typeof item.result === 'number'
                          ? item.result.toLocaleString()
                          : item.result}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(item.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
