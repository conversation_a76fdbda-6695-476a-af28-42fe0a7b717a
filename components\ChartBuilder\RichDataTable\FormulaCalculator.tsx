'use client'

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Calculator, AlertCircle, Copy, Trash2 } from "lucide-react"
import { toast } from "sonner"
import * as math from 'mathjs'
import { FormulaResult, FormulaHistory, FormulaFunction, CellReference } from './types'

interface FormulaCalculatorProps {
  data: any[]
  columns: string[]
  onClose?: () => void
  onCellClick?: (cellRef: string) => void
  onColumnClick?: (columnRef: string) => void
}

// Comprehensive Excel-like functions
const FORMULA_FUNCTIONS: FormulaFunction[] = [
  // Mathematical Functions
  {
    name: 'SUM',
    description: 'Adds all numbers in a range',
    syntax: 'SUM(column_name)',
    example: 'SUM(salary)',
    category: 'math'
  },
  {
    name: 'PRODUCT',
    description: 'Multiplies all numbers in a range',
    syntax: 'PRODUCT(column_name)',
    example: 'PRODUCT(quantity)',
    category: 'math'
  },
  {
    name: 'POWER',
    description: 'Returns a number raised to a power',
    syntax: 'POWER(number, power)',
    example: 'POWER(2, 3)',
    category: 'math'
  },
  {
    name: 'SQRT',
    description: 'Returns the square root of a number',
    syntax: 'SQRT(number)',
    example: 'SQRT(16)',
    category: 'math'
  },
  {
    name: 'ABS',
    description: 'Returns the absolute value of a number',
    syntax: 'ABS(number)',
    example: 'ABS(-5)',
    category: 'math'
  },
  {
    name: 'ROUND',
    description: 'Rounds a number to specified decimals',
    syntax: 'ROUND(number, decimals)',
    example: 'ROUND(3.14159, 2)',
    category: 'math'
  },
  {
    name: 'CEILING',
    description: 'Rounds a number up to the nearest integer',
    syntax: 'CEILING(number)',
    example: 'CEILING(4.2)',
    category: 'math'
  },
  {
    name: 'FLOOR',
    description: 'Rounds a number down to the nearest integer',
    syntax: 'FLOOR(number)',
    example: 'FLOOR(4.8)',
    category: 'math'
  },
  {
    name: 'MOD',
    description: 'Returns the remainder after division',
    syntax: 'MOD(number, divisor)',
    example: 'MOD(10, 3)',
    category: 'math'
  },

  // Statistical Functions
  {
    name: 'AVERAGE',
    description: 'Calculates the average of numbers',
    syntax: 'AVERAGE(column_name)',
    example: 'AVERAGE(age)',
    category: 'statistical'
  },
  {
    name: 'MEDIAN',
    description: 'Returns the median of numbers',
    syntax: 'MEDIAN(column_name)',
    example: 'MEDIAN(salary)',
    category: 'statistical'
  },
  {
    name: 'MODE',
    description: 'Returns the most frequently occurring value',
    syntax: 'MODE(column_name)',
    example: 'MODE(department)',
    category: 'statistical'
  },
  {
    name: 'COUNT',
    description: 'Counts non-empty cells',
    syntax: 'COUNT(column_name)',
    example: 'COUNT(employee_id)',
    category: 'statistical'
  },
  {
    name: 'COUNTA',
    description: 'Counts non-empty cells including text',
    syntax: 'COUNTA(column_name)',
    example: 'COUNTA(name)',
    category: 'statistical'
  },
  {
    name: 'COUNTIF',
    description: 'Counts cells that meet criteria',
    syntax: 'COUNTIF(column_name, criteria)',
    example: 'COUNTIF(department, "IT")',
    category: 'statistical'
  },
  {
    name: 'MIN',
    description: 'Finds the minimum value',
    syntax: 'MIN(column_name)',
    example: 'MIN(salary)',
    category: 'statistical'
  },
  {
    name: 'MAX',
    description: 'Finds the maximum value',
    syntax: 'MAX(column_name)',
    example: 'MAX(salary)',
    category: 'statistical'
  },
  {
    name: 'STDEV',
    description: 'Calculates standard deviation',
    syntax: 'STDEV(column_name)',
    example: 'STDEV(salary)',
    category: 'statistical'
  },
  {
    name: 'VAR',
    description: 'Calculates variance',
    syntax: 'VAR(column_name)',
    example: 'VAR(age)',
    category: 'statistical'
  },

  // Logical Functions
  {
    name: 'IF',
    description: 'Returns one value if condition is true, another if false',
    syntax: 'IF(condition, true_value, false_value)',
    example: 'IF(AVERAGE(salary) > 50000, "High", "Low")',
    category: 'logical'
  },
  {
    name: 'AND',
    description: 'Returns true if all conditions are true',
    syntax: 'AND(condition1, condition2)',
    example: 'AND(MIN(age) >= 18, MAX(age) <= 65)',
    category: 'logical'
  },
  {
    name: 'OR',
    description: 'Returns true if any condition is true',
    syntax: 'OR(condition1, condition2)',
    example: 'OR(SUM(salary) > 100000, COUNT(employee_id) > 10)',
    category: 'logical'
  },
  {
    name: 'NOT',
    description: 'Reverses the logic of its argument',
    syntax: 'NOT(condition)',
    example: 'NOT(AVERAGE(age) < 30)',
    category: 'logical'
  },

  // Text Functions
  {
    name: 'CONCATENATE',
    description: 'Joins text strings together',
    syntax: 'CONCATENATE(text1, text2)',
    example: 'CONCATENATE("Total: ", SUM(salary))',
    category: 'text'
  },
  {
    name: 'LEN',
    description: 'Returns the length of text',
    syntax: 'LEN(text)',
    example: 'LEN("Hello World")',
    category: 'text'
  },
  {
    name: 'UPPER',
    description: 'Converts text to uppercase',
    syntax: 'UPPER(text)',
    example: 'UPPER("hello")',
    category: 'text'
  },
  {
    name: 'LOWER',
    description: 'Converts text to lowercase',
    syntax: 'LOWER(text)',
    example: 'LOWER("HELLO")',
    category: 'text'
  },

  // Date Functions
  {
    name: 'TODAY',
    description: 'Returns today\'s date',
    syntax: 'TODAY()',
    example: 'TODAY()',
    category: 'date'
  },
  {
    name: 'NOW',
    description: 'Returns current date and time',
    syntax: 'NOW()',
    example: 'NOW()',
    category: 'date'
  },
  {
    name: 'YEAR',
    description: 'Extracts year from date',
    syntax: 'YEAR(date)',
    example: 'YEAR(TODAY())',
    category: 'date'
  },
  {
    name: 'MONTH',
    description: 'Extracts month from date',
    syntax: 'MONTH(date)',
    example: 'MONTH(TODAY())',
    category: 'date'
  },
  {
    name: 'DAY',
    description: 'Extracts day from date',
    syntax: 'DAY(date)',
    example: 'DAY(TODAY())',
    category: 'date'
  },

  // Advanced Mathematical Functions
  {
    name: 'EXP',
    description: 'Returns e raised to the power of number',
    syntax: 'EXP(number)',
    example: 'EXP(1)',
    category: 'math'
  },
  {
    name: 'LN',
    description: 'Returns the natural logarithm',
    syntax: 'LN(number)',
    example: 'LN(10)',
    category: 'math'
  },
  {
    name: 'LOG10',
    description: 'Returns the base-10 logarithm',
    syntax: 'LOG10(number)',
    example: 'LOG10(100)',
    category: 'math'
  },
  {
    name: 'SIN',
    description: 'Returns the sine of an angle',
    syntax: 'SIN(angle)',
    example: 'SIN(PI()/2)',
    category: 'math'
  },
  {
    name: 'COS',
    description: 'Returns the cosine of an angle',
    syntax: 'COS(angle)',
    example: 'COS(0)',
    category: 'math'
  },
  {
    name: 'TAN',
    description: 'Returns the tangent of an angle',
    syntax: 'TAN(angle)',
    example: 'TAN(PI()/4)',
    category: 'math'
  },
  {
    name: 'PI',
    description: 'Returns the value of pi',
    syntax: 'PI()',
    example: 'PI()',
    category: 'math'
  },

  // Advanced Statistical Functions
  {
    name: 'MEDIAN',
    description: 'Returns the median of numbers',
    syntax: 'MEDIAN(range)',
    example: 'MEDIAN(A1:A10)',
    category: 'statistical'
  },
  {
    name: 'STDEV',
    description: 'Calculates standard deviation',
    syntax: 'STDEV(range)',
    example: 'STDEV(A1:A10)',
    category: 'statistical'
  },

  // Advanced Text Functions
  {
    name: 'LEFT',
    description: 'Returns leftmost characters',
    syntax: 'LEFT(text, num_chars)',
    example: 'LEFT("Hello", 2)',
    category: 'text'
  },
  {
    name: 'RIGHT',
    description: 'Returns rightmost characters',
    syntax: 'RIGHT(text, num_chars)',
    example: 'RIGHT("Hello", 2)',
    category: 'text'
  },
  {
    name: 'MID',
    description: 'Returns characters from middle of text',
    syntax: 'MID(text, start, length)',
    example: 'MID("Hello", 2, 3)',
    category: 'text'
  },
  {
    name: 'TRIM',
    description: 'Removes extra spaces',
    syntax: 'TRIM(text)',
    example: 'TRIM(" Hello ")',
    category: 'text'
  },

  // Advanced Logical Functions
  {
    name: 'ISNUMBER',
    description: 'Tests if value is a number',
    syntax: 'ISNUMBER(value)',
    example: 'ISNUMBER(A1)',
    category: 'logical'
  },
  {
    name: 'ISTEXT',
    description: 'Tests if value is text',
    syntax: 'ISTEXT(value)',
    example: 'ISTEXT(A1)',
    category: 'logical'
  },
  {
    name: 'ISBLANK',
    description: 'Tests if cell is blank',
    syntax: 'ISBLANK(cell)',
    example: 'ISBLANK(A1)',
    category: 'logical'
  },

  // Financial Functions
  {
    name: 'PMT',
    description: 'Calculates loan payment',
    syntax: 'PMT(rate, nper, pv)',
    example: 'PMT(0.05/12, 360, 100000)',
    category: 'math'
  },
  {
    name: 'FV',
    description: 'Calculates future value',
    syntax: 'FV(rate, nper, pmt, pv)',
    example: 'FV(0.05, 10, -1000, 0)',
    category: 'math'
  }
]

export function FormulaCalculator({ data, columns, onClose, onCellClick, onColumnClick }: FormulaCalculatorProps) {
  const [formula, setFormula] = useState('')
  const [result, setResult] = useState<FormulaResult | null>(null)
  const [history, setHistory] = useState<FormulaHistory[]>([])
  const [selectedFunction, setSelectedFunction] = useState<FormulaFunction | null>(null)
  const [cursorPosition, setCursorPosition] = useState(0)
  const [isCalculating, setIsCalculating] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const formulaInputRef = useRef<HTMLInputElement>(null)

  // Helper function to convert number to Excel column name (0->A, 1->B, 25->Z, 26->AA)
  const getExcelColumnName = useCallback((index: number): string => {
    let result = ''
    while (index >= 0) {
      result = String.fromCharCode(65 + (index % 26)) + result
      index = Math.floor(index / 26) - 1
    }
    return result
  }, [])

  // Helper function to convert Excel column name to index (A->0, B->1, AA->26)
  const getColumnIndex = useCallback((columnName: string): number => {
    let result = 0
    for (let i = 0; i < columnName.length; i++) {
      result = result * 26 + (columnName.charCodeAt(i) - 64)
    }
    return result - 1
  }, [])

  // Create Excel-like column mapping (A, B, C, etc.)
  const columnMapping = useMemo(() => {
    const mapping: Record<string, string> = {}
    const reverseMapping: Record<string, string> = {}

    columns.forEach((column, index) => {
      const excelColumn = getExcelColumnName(index)
      mapping[excelColumn] = column
      reverseMapping[column] = excelColumn
    })

    return { mapping, reverseMapping }
  }, [columns, getExcelColumnName])

  // Helper function to parse Excel cell reference (A1, B2, etc.)
  const parseCellReference = useCallback((cellRef: string): { column: string, row: number } | null => {
    const match = cellRef.match(/^([A-Z]+)(\d+)$/)
    if (!match) return null

    const columnName = match[1]
    const rowNumber = parseInt(match[2]) - 1 // Convert to 0-based index
    const actualColumn = columnMapping.mapping[columnName]

    if (!actualColumn || rowNumber < 0 || rowNumber >= data.length) return null

    return { column: actualColumn, row: rowNumber }
  }, [columnMapping, data])

  // Get cell value by Excel reference (A1, B2, etc.)
  const getCellValue = useCallback((cellRef: string): any => {
    const parsed = parseCellReference(cellRef)
    if (!parsed) return 0

    const value = data[parsed.row]?.[parsed.column]
    return value !== null && value !== undefined ? value : 0
  }, [parseCellReference, data])

  // Advanced formula validation
  const validateFormula = useCallback((formulaText: string): string[] => {
    const errors: string[] = []

    // Check for basic syntax errors
    if (formulaText.trim() === '') return errors

    // Check for balanced parentheses
    let parenCount = 0
    for (const char of formulaText) {
      if (char === '(') parenCount++
      if (char === ')') parenCount--
      if (parenCount < 0) {
        errors.push('Unmatched closing parenthesis')
        break
      }
    }
    if (parenCount > 0) {
      errors.push('Unmatched opening parenthesis')
    }

    // Check for valid cell references
    const cellRefs = formulaText.match(/[A-Z]+\d+/g) || []
    for (const cellRef of cellRefs) {
      const parsed = parseCellReference(cellRef)
      if (!parsed) {
        errors.push(`Invalid cell reference: ${cellRef}`)
      }
    }

    // Check for valid column references
    const colRefs = formulaText.match(/[A-Z]+:[A-Z]+/g) || []
    for (const colRef of colRefs) {
      const [startCol, endCol] = colRef.split(':')
      if (!columnMapping.mapping[startCol] || !columnMapping.mapping[endCol]) {
        errors.push(`Invalid column reference: ${colRef}`)
      }
    }

    // Check for division by zero
    if (formulaText.includes('/0') || formulaText.includes('/ 0')) {
      errors.push('Division by zero detected')
    }

    return errors
  }, [parseCellReference, columnMapping])

  // Enhanced error handling for different error types
  const handleCalculationError = useCallback((error: any): string => {
    if (error.message) {
      // Math.js specific errors
      if (error.message.includes('Unexpected type')) {
        return 'Type mismatch: Check that you\'re using numbers where expected'
      }
      if (error.message.includes('Undefined symbol')) {
        return 'Unknown function or variable'
      }
      if (error.message.includes('Unexpected token')) {
        return 'Syntax error: Check your formula syntax'
      }
      if (error.message.includes('Division by zero')) {
        return 'Division by zero error'
      }
      if (error.message.includes('out of range')) {
        return 'Value out of range'
      }
      return error.message
    }
    return 'Unknown calculation error'
  }, [])

  // Calculate comprehensive column statistics for reference
  const columnStats = useMemo(() => {
    const stats: Record<string, any> = {}

    columns.forEach((column, index) => {
      const values = data.map(row => row[column]).filter(val => val !== null && val !== undefined)
      const numericValues = values.filter(val => typeof val === 'number' || !isNaN(Number(val))).map(Number)
      const textValues = values.filter(val => typeof val === 'string')

      // Calculate median
      const sortedNumeric = [...numericValues].sort((a, b) => a - b)
      const median = sortedNumeric.length > 0
        ? sortedNumeric.length % 2 === 0
          ? (sortedNumeric[sortedNumeric.length / 2 - 1] + sortedNumeric[sortedNumeric.length / 2]) / 2
          : sortedNumeric[Math.floor(sortedNumeric.length / 2)]
        : 0

      // Calculate mode
      const frequency: Record<string, number> = {}
      values.forEach(val => {
        const key = String(val)
        frequency[key] = (frequency[key] || 0) + 1
      })
      const mode = Object.keys(frequency).reduce((a, b) => frequency[a] > frequency[b] ? a : b, '')

      // Calculate standard deviation and variance
      const mean = numericValues.length > 0 ? numericValues.reduce((a, b) => a + b, 0) / numericValues.length : 0
      const variance = numericValues.length > 0
        ? numericValues.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / numericValues.length
        : 0
      const stdev = Math.sqrt(variance)

      // Calculate product
      const product = numericValues.length > 0 ? numericValues.reduce((a, b) => a * b, 1) : 0

      const excelColumn = getExcelColumnName(index)

      stats[column] = {
        count: values.length,
        counta: values.length,
        numericCount: numericValues.length,
        textCount: textValues.length,
        sum: numericValues.length > 0 ? numericValues.reduce((a, b) => a + b, 0) : 0,
        product,
        avg: mean,
        median,
        mode,
        min: numericValues.length > 0 ? Math.min(...numericValues) : 0,
        max: numericValues.length > 0 ? Math.max(...numericValues) : 0,
        stdev,
        variance,
        type: numericValues.length > 0 ? 'number' : 'text',
        values: numericValues,
        allValues: values,
        excelColumn
      }
    })

    return stats
  }, [data, columns, columnMapping])

  // Excel-like formula evaluation with exact Excel syntax
  const evaluateFormula = useCallback((formulaText: string): FormulaResult => {
    setIsCalculating(true)

    try {
      let processedFormula = formulaText.trim()

      // Pre-validation
      const validationErrors = validateFormula(formulaText)
      if (validationErrors.length > 0) {
        setValidationErrors(validationErrors)
        return {
          formula: formulaText,
          result: null,
          error: validationErrors[0],
          timestamp: Date.now()
        }
      }

      setValidationErrors([])

      // Remove leading = if present (Excel style)
      if (processedFormula.startsWith('=')) {
        processedFormula = processedFormula.substring(1)
      }

      // Replace Excel cell references (A1, B2, etc.) with actual values
      processedFormula = processedFormula.replace(/([A-Z]+)(\d+)/g, (match, col, row) => {
        const cellValue = getCellValue(match)
        return typeof cellValue === 'number' ? cellValue.toString() : `"${cellValue}"`
      })

      // Replace Excel range references (A1:A10, B2:D5, etc.) with arrays
      processedFormula = processedFormula.replace(/([A-Z]+)(\d+):([A-Z]+)(\d+)/g, (match, startCol, startRow, endCol, endRow) => {
        const startColIndex = getColumnIndex(startCol)
        const endColIndex = getColumnIndex(endCol)
        const startRowIndex = parseInt(startRow) - 1
        const endRowIndex = parseInt(endRow) - 1

        const values: number[] = []
        for (let row = startRowIndex; row <= endRowIndex; row++) {
          for (let col = startColIndex; col <= endColIndex; col++) {
            const columnName = getExcelColumnName(col)
            const cellRef = `${columnName}${row + 1}`
            const value = getCellValue(cellRef)
            if (typeof value === 'number' || !isNaN(Number(value))) {
              values.push(Number(value))
            }
          }
        }
        return `[${values.join(',')}]`
      })

      // Replace Excel column references (A:A, B:B, etc.) with full column arrays
      processedFormula = processedFormula.replace(/([A-Z]+):([A-Z]+)/g, (match, startCol, endCol) => {
        if (startCol === endCol) {
          // Single column reference like A:A
          const actualColumn = columnMapping.mapping[startCol]
          if (actualColumn && columnStats[actualColumn]) {
            return `[${columnStats[actualColumn].values.join(',')}]`
          }
        }
        return '[]'
      })

      // Replace Excel functions with exact Excel behavior
      processedFormula = processedFormula
        // SUM function - works with ranges and individual cells
        .replace(/SUM\(([^)]+)\)/gi, (match, args) => {
          // If it's already processed as an array, sum it
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            return values.reduce((sum: number, val: number) => sum + val, 0).toString()
          }
          // If it's a column name, get the sum
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            return columnStats[actualColumn].sum.toString()
          }
          return '0'
        })

        // AVERAGE function
        .replace(/AVERAGE\(([^)]+)\)/gi, (match, args) => {
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            return values.length > 0 ? (values.reduce((sum: number, val: number) => sum + val, 0) / values.length).toString() : '0'
          }
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            return columnStats[actualColumn].avg.toString()
          }
          return '0'
        })

        // COUNT function
        .replace(/COUNT\(([^)]+)\)/gi, (match, args) => {
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            return values.length.toString()
          }
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            return columnStats[actualColumn].numericCount.toString()
          }
          return '0'
        })

        // MIN function
        .replace(/MIN\(([^)]+)\)/gi, (match, args) => {
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            return values.length > 0 ? Math.min(...values).toString() : '0'
          }
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            return columnStats[actualColumn].min.toString()
          }
          return '0'
        })

        // MAX function
        .replace(/MAX\(([^)]+)\)/gi, (match, args) => {
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            return values.length > 0 ? Math.max(...values).toString() : '0'
          }
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            return columnStats[actualColumn].max.toString()
          }
          return '0'
        })

        // ROUND function
        .replace(/ROUND\(([^,]+),\s*([^)]+)\)/gi, (match, number, decimals) => {
          return `round(${number}, ${decimals})`
        })

        // IF function
        .replace(/IF\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, condition, trueVal, falseVal) => {
          return `(${condition}) ? (${trueVal}) : (${falseVal})`
        })

        // AND function
        .replace(/AND\(([^)]+)\)/gi, (match, conditions) => {
          const conds = conditions.split(',').map((c: string) => c.trim())
          return `(${conds.join(' && ')})`
        })

        // OR function
        .replace(/OR\(([^)]+)\)/gi, (match, conditions) => {
          const conds = conditions.split(',').map((c: string) => c.trim())
          return `(${conds.join(' || ')})`
        })

        // CONCATENATE function
        .replace(/CONCATENATE\(([^)]+)\)/gi, (match, args) => {
          const parts = args.split(',').map((p: string) => p.trim())
          return parts.join(' + ')
        })

        // Mathematical functions
        .replace(/POWER\(([^,]+),\s*([^)]+)\)/gi, (match, base, exp) => `pow(${base}, ${exp})`)
        .replace(/SQRT\(([^)]+)\)/gi, (match, num) => `sqrt(${num})`)
        .replace(/ABS\(([^)]+)\)/gi, (match, num) => `abs(${num})`)
        .replace(/EXP\(([^)]+)\)/gi, (match, num) => `exp(${num})`)
        .replace(/LN\(([^)]+)\)/gi, (match, num) => `log(${num})`)
        .replace(/LOG10\(([^)]+)\)/gi, (match, num) => `log10(${num})`)
        .replace(/SIN\(([^)]+)\)/gi, (match, num) => `sin(${num})`)
        .replace(/COS\(([^)]+)\)/gi, (match, num) => `cos(${num})`)
        .replace(/TAN\(([^)]+)\)/gi, (match, num) => `tan(${num})`)
        .replace(/PI\(\)/gi, () => 'pi')

        // Statistical functions
        .replace(/MEDIAN\(([^)]+)\)/gi, (match, args) => {
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n)).sort((a: number, b: number) => a - b)
            if (values.length === 0) return '0'
            const mid = Math.floor(values.length / 2)
            return values.length % 2 === 0
              ? ((values[mid - 1] + values[mid]) / 2).toString()
              : values[mid].toString()
          }
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            return columnStats[actualColumn].median.toString()
          }
          return '0'
        })
        .replace(/STDEV\(([^)]+)\)/gi, (match, args) => {
          if (args.startsWith('[') && args.endsWith(']')) {
            const values = args.slice(1, -1).split(',').map(Number).filter((n: number) => !isNaN(n))
            if (values.length <= 1) return '0'
            const mean = values.reduce((sum: number, val: number) => sum + val, 0) / values.length
            const variance = values.reduce((acc: number, val: number) => acc + Math.pow(val - mean, 2), 0) / (values.length - 1)
            return Math.sqrt(variance).toString()
          }
          const actualColumn = columnMapping.mapping[args] || args
          if (columnStats[actualColumn]) {
            return columnStats[actualColumn].stdev.toString()
          }
          return '0'
        })

        // Text functions
        .replace(/LEFT\(([^,]+),\s*([^)]+)\)/gi, (match, text, num) => `substring(${text}, 0, ${num})`)
        .replace(/RIGHT\(([^,]+),\s*([^)]+)\)/gi, (match, text, num) => `substring(${text}, length(${text}) - ${num})`)
        .replace(/MID\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, text, start, length) => `substring(${text}, ${start} - 1, ${start} - 1 + ${length})`)
        .replace(/UPPER\(([^)]+)\)/gi, (match, text) => `uppercase(${text})`)
        .replace(/LOWER\(([^)]+)\)/gi, (match, text) => `lowercase(${text})`)
        .replace(/LEN\(([^)]+)\)/gi, (match, text) => `length(${text})`)
        .replace(/TRIM\(([^)]+)\)/gi, (match, text) => `trim(${text})`)

        // Logical functions
        .replace(/NOT\(([^)]+)\)/gi, (match, condition) => `!(${condition})`)
        .replace(/ISNUMBER\(([^)]+)\)/gi, (match, value) => `isNumber(${value})`)
        .replace(/ISTEXT\(([^)]+)\)/gi, (match, value) => `isText(${value})`)
        .replace(/ISBLANK\(([^)]+)\)/gi, (match, value) => `isBlank(${value})`)

        // Date functions
        .replace(/TODAY\(\)/gi, () => `"${new Date().toISOString().split('T')[0]}"`)
        .replace(/NOW\(\)/gi, () => `"${new Date().toISOString()}"`)
        .replace(/YEAR\(([^)]+)\)/gi, (match, date) => `year(${date})`)
        .replace(/MONTH\(([^)]+)\)/gi, (match, date) => `month(${date})`)
        .replace(/DAY\(([^)]+)\)/gi, (match, date) => `day(${date})`)
        .replace(/WEEKDAY\(([^)]+)\)/gi, (match, date) => `weekday(${date})`)

        // Financial functions
        .replace(/PMT\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, rate, nper, pv) => `pmt(${rate}, ${nper}, ${pv})`)
        .replace(/FV\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, rate, nper, pmt, pv) => `fv(${rate}, ${nper}, ${pmt}, ${pv})`)

        // Advanced Excel functions
        .replace(/SUMIF\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, range, criteria, sumRange) => {
          return `sumif(${range}, ${criteria}, ${sumRange})`
        })
        .replace(/AVERAGEIF\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, range, criteria, avgRange) => {
          return `averageif(${range}, ${criteria}, ${avgRange})`
        })
        .replace(/SUMIFS\(([^)]+)\)/gi, (match, args) => {
          return `sumifs(${args})`
        })
        .replace(/INDEX\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, array, row, col) => {
          return `index(${array}, ${row}, ${col})`
        })
        .replace(/MATCH\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, lookup, array, type) => {
          return `match(${lookup}, ${array}, ${type})`
        })
        .replace(/VLOOKUP\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, lookup, table, col, exact) => {
          return `vlookup(${lookup}, ${table}, ${col}, ${exact})`
        })
        .replace(/HLOOKUP\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, lookup, table, row, exact) => {
          return `hlookup(${lookup}, ${table}, ${row}, ${exact})`
        })

        // Array functions
        .replace(/TRANSPOSE\(([^)]+)\)/gi, (match, array) => {
          return `transpose(${array})`
        })
        .replace(/SORT\(([^)]+)\)/gi, (match, array) => {
          return `sort(${array})`
        })
        .replace(/UNIQUE\(([^)]+)\)/gi, (match, array) => {
          return `unique(${array})`
        })

        // Advanced statistical functions
        .replace(/PERCENTILE\(([^,]+),\s*([^)]+)\)/gi, (match, array, k) => {
          return `percentile(${array}, ${k})`
        })
        .replace(/QUARTILE\(([^,]+),\s*([^)]+)\)/gi, (match, array, quart) => {
          return `quartile(${array}, ${quart})`
        })
        .replace(/RANK\(([^,]+),\s*([^,]+),\s*([^)]+)\)/gi, (match, number, ref, order) => {
          return `rank(${number}, ${ref}, ${order})`
        })

        // Advanced math functions
        .replace(/GCD\(([^)]+)\)/gi, (match, args) => {
          return `gcd(${args})`
        })
        .replace(/LCM\(([^)]+)\)/gi, (match, args) => {
          return `lcm(${args})`
        })
        .replace(/FACT\(([^)]+)\)/gi, (match, num) => {
          return `factorial(${num})`
        })
        .replace(/COMBIN\(([^,]+),\s*([^)]+)\)/gi, (match, n, k) => {
          return `combinations(${n}, ${k})`
        })
        .replace(/PERMUT\(([^,]+),\s*([^)]+)\)/gi, (match, n, k) => {
          return `permutations(${n}, ${k})`
        })

      // Create evaluation scope with comprehensive Excel-like functions
      const scope = {
        // Mathematical functions
        round: (num: number, decimals: number) => Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals),
        pow: Math.pow,
        sqrt: Math.sqrt,
        abs: Math.abs,
        exp: Math.exp,
        log: Math.log,
        log10: (num: number) => Math.log10(num),
        sin: Math.sin,
        cos: Math.cos,
        tan: Math.tan,
        pi: Math.PI,
        e: Math.E,

        // Text functions
        length: (str: string) => String(str).length,
        uppercase: (str: string) => String(str).toUpperCase(),
        lowercase: (str: string) => String(str).toLowerCase(),
        substring: (str: string, start: number, end?: number) => String(str).substring(start, end),
        trim: (str: string) => String(str).trim(),

        // Date functions
        year: (date: string) => new Date(date).getFullYear(),
        month: (date: string) => new Date(date).getMonth() + 1,
        day: (date: string) => new Date(date).getDate(),
        weekday: (date: string) => new Date(date).getDay() + 1,

        // Logical functions
        isNumber: (value: any) => typeof value === 'number' && !isNaN(value),
        isText: (value: any) => typeof value === 'string',
        isBlank: (value: any) => value === null || value === undefined || value === '',

        // Financial functions (simplified)
        pmt: (rate: number, nper: number, pv: number) => {
          if (rate === 0) return -pv / nper
          return -pv * (rate * Math.pow(1 + rate, nper)) / (Math.pow(1 + rate, nper) - 1)
        },
        fv: (rate: number, nper: number, pmt: number, pv: number = 0) => {
          if (rate === 0) return -pv - pmt * nper
          return -pv * Math.pow(1 + rate, nper) - pmt * ((Math.pow(1 + rate, nper) - 1) / rate)
        },

        // Advanced Excel functions
        sumif: (range: number[], criteria: any, sumRange?: number[]) => {
          // Simplified SUMIF implementation
          const targetRange = sumRange || range
          return range.reduce((sum, val, i) => {
            if (val === criteria || (typeof criteria === 'string' && String(val).includes(criteria))) {
              return sum + (targetRange[i] || 0)
            }
            return sum
          }, 0)
        },

        averageif: (range: number[], criteria: any, avgRange?: number[]) => {
          const targetRange = avgRange || range
          let sum = 0
          let count = 0
          range.forEach((val, i) => {
            if (val === criteria || (typeof criteria === 'string' && String(val).includes(criteria))) {
              sum += targetRange[i] || 0
              count++
            }
          })
          return count > 0 ? sum / count : 0
        },

        percentile: (array: number[], k: number) => {
          const sorted = [...array].sort((a, b) => a - b)
          const index = (sorted.length - 1) * k
          const lower = Math.floor(index)
          const upper = Math.ceil(index)
          const weight = index % 1

          if (upper >= sorted.length) return sorted[sorted.length - 1]
          return sorted[lower] * (1 - weight) + sorted[upper] * weight
        },

        quartile: (array: number[], quart: number) => {
          const percentiles = [0, 0.25, 0.5, 0.75, 1]
          return scope.percentile(array, percentiles[quart] || 0.5)
        },

        rank: (number: number, ref: number[], order: number = 0) => {
          const sorted = [...ref].sort((a, b) => order === 0 ? b - a : a - b)
          return sorted.indexOf(number) + 1
        },

        factorial: (n: number): number => {
          if (n <= 1) return 1
          return n * scope.factorial(n - 1)
        },

        combinations: (n: number, k: number) => {
          return scope.factorial(n) / (scope.factorial(k) * scope.factorial(n - k))
        },

        permutations: (n: number, k: number) => {
          return scope.factorial(n) / scope.factorial(n - k)
        },

        gcd: (...args: number[]) => {
          const gcdTwo = (a: number, b: number): number => b === 0 ? a : gcdTwo(b, a % b)
          return args.reduce(gcdTwo)
        },

        lcm: (...args: number[]) => {
          const lcmTwo = (a: number, b: number) => Math.abs(a * b) / scope.gcd(a, b)
          return args.reduce(lcmTwo)
        },

        // Lookup functions (simplified)
        vlookup: (lookup: any, table: any, col: number, exact: boolean) => {
          // Simplified implementation - would need more complex logic for real VLOOKUP
          return lookup
        },

        hlookup: (lookup: any, table: any, row: number, exact: boolean) => {
          // Simplified implementation
          return lookup
        },

        index: (array: any[], row: number, col: number) => {
          // Simplified implementation
          return array[row - 1] || null
        },

        match: (lookup: any, array: any[], type: number) => {
          // Simplified implementation
          const index = array.indexOf(lookup)
          return index >= 0 ? index + 1 : null
        }
      }

      const calculatedResult = math.evaluate(processedFormula, scope)

      // Post-process result for Excel compatibility
      let finalResult = calculatedResult
      if (typeof finalResult === 'number') {
        // Handle Excel-like number formatting
        if (isNaN(finalResult)) finalResult = '#NUM!'
        if (!isFinite(finalResult)) finalResult = '#DIV/0!'
        if (finalResult > 1e15) finalResult = '#NUM!'
        if (finalResult < -1e15) finalResult = '#NUM!'
      }

      return {
        formula: formulaText,
        result: finalResult,
        timestamp: Date.now()
      }
    } catch (error: any) {
      const enhancedError = handleCalculationError(error)
      return {
        formula: formulaText,
        result: null,
        error: enhancedError,
        timestamp: Date.now()
      }
    } finally {
      setIsCalculating(false)
    }
  }, [columnStats, columnMapping, getCellValue, getColumnIndex, getExcelColumnName, validateFormula, handleCalculationError])

  // Handle formula calculation
  const handleCalculate = () => {
    if (!formula.trim()) {
      toast.error('Please enter a formula')
      return
    }

    const calculationResult = evaluateFormula(formula)
    setResult(calculationResult)

    // Add to history if successful
    if (calculationResult.result !== null) {
      const historyItem: FormulaHistory = {
        id: Date.now().toString(),
        formula: calculationResult.formula,
        result: calculationResult.result,
        timestamp: calculationResult.timestamp
      }
      setHistory(prev => [historyItem, ...prev.slice(0, 9)]) // Keep last 10 items
      toast.success('Formula calculated successfully')
    } else {
      toast.error(calculationResult.error || 'Calculation failed')
    }
  }

  // Insert function into formula
  const insertFunction = (func: FormulaFunction) => {
    const insertion = func.syntax
    setFormula(prev => prev + insertion)
    setSelectedFunction(func)
  }

  // Insert column name into formula
  const insertColumn = (column: string) => {
    setFormula(prev => prev + column)
  }

  // Insert text at cursor position
  const insertAtCursor = useCallback((text: string) => {
    const input = formulaInputRef.current
    if (input) {
      const start = input.selectionStart || 0
      const end = input.selectionEnd || 0
      const currentFormula = formula

      const newFormula = currentFormula.slice(0, start) + text + currentFormula.slice(end)
      setFormula(newFormula)

      // Set cursor position after inserted text
      setTimeout(() => {
        const newPosition = start + text.length
        input.setSelectionRange(newPosition, newPosition)
        input.focus()
        setCursorPosition(newPosition)
      }, 0)
    } else {
      // Fallback if ref is not available
      setFormula(prev => prev + text)
    }
  }, [formula])

  // Add cell reference to formula (called from table clicks)
  const addCellReference = useCallback((cellRef: string) => {
    insertAtCursor(cellRef)
  }, [insertAtCursor])

  // Add column reference to formula (called from column header clicks)
  const addColumnReference = useCallback((columnRef: string) => {
    insertAtCursor(columnRef)
  }, [insertAtCursor])

  // Handle cursor position changes
  const handleCursorChange = useCallback((e: any) => {
    setCursorPosition(e.target.selectionStart || 0)
  }, [])

  // Handle formula input changes
  const handleFormulaChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormula(e.target.value)
    setCursorPosition(e.target.selectionStart || 0)

    // Real-time validation (optional)
    if (e.target.value.trim()) {
      const errors = validateFormula(e.target.value)
      setValidationErrors(errors)
    } else {
      setValidationErrors([])
    }
  }, [validateFormula])

  // Expose functions to parent component
  useEffect(() => {
    if (onCellClick) {
      // Store the function reference so parent can call it
      (window as any).addCellToFormula = addCellReference
    }
    if (onColumnClick) {
      // Store the function reference so parent can call it
      (window as any).addColumnToFormula = addColumnReference
    }
  }, [addCellReference, addColumnReference, onCellClick, onColumnClick])

  // Copy result to clipboard
  const copyResult = () => {
    if (result?.result !== null && result?.result !== undefined) {
      navigator.clipboard.writeText(result.result.toString())
      toast.success('Result copied to clipboard')
    }
  }

  // Load formula from history
  const loadFromHistory = (historyItem: FormulaHistory) => {
    setFormula(historyItem.formula)
    setResult({
      formula: historyItem.formula,
      result: historyItem.result,
      timestamp: historyItem.timestamp
    })
  }

  // Clear history
  const clearHistory = () => {
    setHistory([])
    toast.success('History cleared')
  }

  return (
    <div className="w-full bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border rounded-lg p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Calculator className="h-4 w-4 text-blue-600" />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Excel Calculator</h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
        >
          ×
        </Button>
      </div>

      {/* Compact Formula Input */}
      <div className="space-y-3">
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              ref={formulaInputRef}
              value={formula}
              onChange={handleFormulaChange}
              onSelect={handleCursorChange}
              onKeyUp={handleCursorChange}
              onClick={handleCursorChange}
              placeholder="=SUM(A:A), =AVERAGE(A1:A10), =A1+B1*2, etc."
              className={`font-mono text-sm h-8 bg-white dark:bg-gray-800 ${
                validationErrors.length > 0 ? 'border-red-500' : ''
              }`}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleCalculate()
                }
              }}
              disabled={isCalculating}
            />
            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <div className="text-xs text-red-500 mt-1">
                {validationErrors[0]}
              </div>
            )}
          </div>
          <Button
            onClick={handleCalculate}
            size="sm"
            className="h-8 px-4 text-xs"
            disabled={isCalculating || !formula.trim()}
          >
            {isCalculating ? 'Calculating...' : 'Calculate'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setFormula('')
              setValidationErrors([])
              setResult(null)
              formulaInputRef.current?.focus()
            }}
            className="h-8 px-3 text-xs"
          >
            Clear
          </Button>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('SUM(A:A)')}
            className="h-6 px-2 text-xs"
          >
            SUM(A:A)
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('AVERAGE(A:A)')}
            className="h-6 px-2 text-xs"
          >
            AVG(A:A)
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('COUNT(A:A)')}
            className="h-6 px-2 text-xs"
          >
            COUNT(A:A)
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => insertAtCursor('IF(A1>0,"Yes","No")')}
            className="h-6 px-2 text-xs"
          >
            IF()
          </Button>
          {['+', '-', '*', '/', '(', ')', ',', '=', '>', '<'].map((op) => (
            <Button
              key={op}
              variant="outline"
              size="sm"
              onClick={() => insertAtCursor(op)}
              className="h-6 w-6 p-0 text-xs"
            >
              {op}
            </Button>
          ))}
        </div>
      </div>

      {/* Result Display */}
      {result && (
        <div className="mt-3 p-3 bg-white dark:bg-gray-800 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-xs text-gray-500 mb-1">Result:</div>
              <div className="text-lg font-mono">
                {result.error ? (
                  <span className="text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {result.error}
                  </span>
                ) : (
                  <span className="text-green-600 font-semibold">
                    {typeof result.result === 'number'
                      ? result.result.toLocaleString()
                      : result.result}
                  </span>
                )}
              </div>
            </div>
            {result.result !== null && (
              <Button variant="outline" size="sm" onClick={copyResult} className="h-7 w-7 p-0">
                <Copy className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Compact Reference Section */}
      <div className="mt-3">
        <Tabs defaultValue="columns" className="w-full">
          <TabsList className="grid w-full grid-cols-3 h-8">
            <TabsTrigger value="columns" className="text-xs">Columns</TabsTrigger>
            <TabsTrigger value="functions" className="text-xs">Functions</TabsTrigger>
            <TabsTrigger value="history" className="text-xs">History</TabsTrigger>
          </TabsList>

          <TabsContent value="columns" className="mt-2">
            <ScrollArea className="h-24">
              <div className="flex flex-wrap gap-1">
                {columns.map((column, index) => {
                  const excelColumn = String.fromCharCode(65 + (index % 26))
                  return (
                    <Button
                      key={column}
                      variant="outline"
                      size="sm"
                      onClick={() => insertAtCursor(`${excelColumn}:${excelColumn}`)}
                      className="h-6 px-2 text-xs font-mono"
                      title={`${column} (Excel: ${excelColumn}:${excelColumn})`}
                    >
                      {excelColumn}:{excelColumn}
                    </Button>
                  )
                })}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="functions" className="mt-2">
            <ScrollArea className="h-24">
              <div className="flex flex-wrap gap-1">
                {[
                  { func: 'SUM', example: '=SUM(A:A)' },
                  { func: 'AVERAGE', example: '=AVERAGE(A1:A10)' },
                  { func: 'COUNT', example: '=COUNT(A:A)' },
                  { func: 'MIN', example: '=MIN(A:A)' },
                  { func: 'MAX', example: '=MAX(A:A)' },
                  { func: 'IF', example: '=IF(A1>10,"High","Low")' },
                  { func: 'ROUND', example: '=ROUND(A1,2)' },
                  { func: 'AND', example: '=AND(A1>0,B1<100)' },
                  { func: 'OR', example: '=OR(A1>10,B1>20)' }
                ].map(({ func, example }) => (
                  <Button
                    key={func}
                    variant="outline"
                    size="sm"
                    onClick={() => insertAtCursor(example)}
                    className="h-6 px-2 text-xs font-mono"
                    title={`Click to insert: ${example}`}
                  >
                    {func}
                  </Button>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="history" className="mt-2">
            <div className="flex justify-between items-center mb-2">
              <span className="text-xs text-gray-500">Recent calculations</span>
              {history.length > 0 && (
                <Button variant="outline" size="sm" onClick={clearHistory} className="h-6 w-6 p-0">
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
            <ScrollArea className="h-24">
              <div className="space-y-1">
                {history.length === 0 ? (
                  <div className="text-center text-gray-400 py-4 text-xs">
                    No calculations yet
                  </div>
                ) : (
                  history.slice(0, 3).map((item) => (
                    <div
                      key={item.id}
                      className="p-2 border rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      onClick={() => loadFromHistory(item)}
                    >
                      <div className="font-mono text-xs truncate">{item.formula}</div>
                      <div className="text-xs text-green-600">
                        = {typeof item.result === 'number'
                          ? item.result.toLocaleString()
                          : item.result}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
