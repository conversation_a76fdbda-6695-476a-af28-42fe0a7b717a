'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Calculator, History, Function, AlertCircle, Copy, Trash2 } from "lucide-react"
import { toast } from "sonner"
import * as math from 'mathjs'
import { FormulaResult, FormulaHistory, FormulaFunction, CellReference } from './types'

interface FormulaCalculatorProps {
  data: any[]
  columns: string[]
  onClose?: () => void
}

// Predefined Excel-like functions
const FORMULA_FUNCTIONS: FormulaFunction[] = [
  {
    name: 'SUM',
    description: 'Adds all numbers in a range',
    syntax: 'SUM(column_name)',
    example: 'SUM(salary)',
    category: 'math'
  },
  {
    name: 'AVERAGE',
    description: 'Calculates the average of numbers',
    syntax: 'AVERAGE(column_name)',
    example: 'AVERAGE(age)',
    category: 'statistical'
  },
  {
    name: 'COUNT',
    description: 'Counts non-empty cells',
    syntax: 'COUNT(column_name)',
    example: 'COUNT(employee_id)',
    category: 'statistical'
  },
  {
    name: 'MIN',
    description: 'Finds the minimum value',
    syntax: 'MIN(column_name)',
    example: 'MIN(salary)',
    category: 'statistical'
  },
  {
    name: 'MAX',
    description: 'Finds the maximum value',
    syntax: 'MAX(column_name)',
    example: 'MAX(salary)',
    category: 'statistical'
  },
  {
    name: 'ROUND',
    description: 'Rounds a number to specified decimals',
    syntax: 'ROUND(number, decimals)',
    example: 'ROUND(3.14159, 2)',
    category: 'math'
  }
]

export function FormulaCalculator({ data, columns, onClose }: FormulaCalculatorProps) {
  const [formula, setFormula] = useState('')
  const [result, setResult] = useState<FormulaResult | null>(null)
  const [history, setHistory] = useState<FormulaHistory[]>([])
  const [selectedFunction, setSelectedFunction] = useState<FormulaFunction | null>(null)

  // Calculate column statistics for reference
  const columnStats = useMemo(() => {
    const stats: Record<string, any> = {}
    
    columns.forEach(column => {
      const values = data.map(row => row[column]).filter(val => val !== null && val !== undefined)
      const numericValues = values.filter(val => typeof val === 'number' || !isNaN(Number(val))).map(Number)
      
      stats[column] = {
        count: values.length,
        numericCount: numericValues.length,
        sum: numericValues.length > 0 ? numericValues.reduce((a, b) => a + b, 0) : 0,
        avg: numericValues.length > 0 ? numericValues.reduce((a, b) => a + b, 0) / numericValues.length : 0,
        min: numericValues.length > 0 ? Math.min(...numericValues) : 0,
        max: numericValues.length > 0 ? Math.max(...numericValues) : 0,
        type: numericValues.length > 0 ? 'number' : 'text'
      }
    })
    
    return stats
  }, [data, columns])

  // Enhanced formula evaluation with Excel-like functions
  const evaluateFormula = useCallback((formulaText: string): FormulaResult => {
    try {
      let processedFormula = formulaText.trim()
      
      // Replace Excel-like functions with mathjs equivalents
      processedFormula = processedFormula
        .replace(/SUM\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) {
            return columnStats[column].sum.toString()
          }
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/AVERAGE\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) {
            return columnStats[column].avg.toString()
          }
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/COUNT\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) {
            return columnStats[column].count.toString()
          }
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/MIN\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) {
            return columnStats[column].min.toString()
          }
          throw new Error(`Column '${column}' not found`)
        })
        .replace(/MAX\((\w+)\)/gi, (match, column) => {
          if (columnStats[column]) {
            return columnStats[column].max.toString()
          }
          throw new Error(`Column '${column}' not found`)
        })

      // Evaluate using mathjs
      const calculatedResult = math.evaluate(processedFormula)
      
      return {
        formula: formulaText,
        result: calculatedResult,
        timestamp: Date.now()
      }
    } catch (error: any) {
      return {
        formula: formulaText,
        result: null,
        error: error.message || 'Invalid formula',
        timestamp: Date.now()
      }
    }
  }, [columnStats])

  // Handle formula calculation
  const handleCalculate = () => {
    if (!formula.trim()) {
      toast.error('Please enter a formula')
      return
    }

    const calculationResult = evaluateFormula(formula)
    setResult(calculationResult)

    // Add to history if successful
    if (calculationResult.result !== null) {
      const historyItem: FormulaHistory = {
        id: Date.now().toString(),
        formula: calculationResult.formula,
        result: calculationResult.result,
        timestamp: calculationResult.timestamp
      }
      setHistory(prev => [historyItem, ...prev.slice(0, 9)]) // Keep last 10 items
      toast.success('Formula calculated successfully')
    } else {
      toast.error(calculationResult.error || 'Calculation failed')
    }
  }

  // Insert function into formula
  const insertFunction = (func: FormulaFunction) => {
    const insertion = func.syntax
    setFormula(prev => prev + insertion)
    setSelectedFunction(func)
  }

  // Insert column name into formula
  const insertColumn = (column: string) => {
    setFormula(prev => prev + column)
  }

  // Copy result to clipboard
  const copyResult = () => {
    if (result?.result !== null) {
      navigator.clipboard.writeText(result.result.toString())
      toast.success('Result copied to clipboard')
    }
  }

  // Load formula from history
  const loadFromHistory = (historyItem: FormulaHistory) => {
    setFormula(historyItem.formula)
    setResult({
      formula: historyItem.formula,
      result: historyItem.result,
      timestamp: historyItem.timestamp
    })
  }

  // Clear history
  const clearHistory = () => {
    setHistory([])
    toast.success('History cleared')
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Calculator className="h-5 w-5" />
          Formula Calculator
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Formula Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Formula</label>
          <div className="flex gap-2">
            <Input
              value={formula}
              onChange={(e) => setFormula(e.target.value)}
              placeholder="Enter formula (e.g., SUM(salary) + AVERAGE(bonus))"
              className="font-mono text-sm"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleCalculate()
                }
              }}
            />
            <Button onClick={handleCalculate} size="sm">
              Calculate
            </Button>
          </div>
        </div>

        {/* Result Display */}
        {result && (
          <div className="p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">Result:</div>
                <div className="text-lg font-mono">
                  {result.error ? (
                    <span className="text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {result.error}
                    </span>
                  ) : (
                    <span className="text-green-600">
                      {typeof result.result === 'number' 
                        ? result.result.toLocaleString() 
                        : result.result}
                    </span>
                  )}
                </div>
              </div>
              {result.result !== null && (
                <Button variant="outline" size="sm" onClick={copyResult}>
                  <Copy className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        )}

        <Tabs defaultValue="functions" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="functions">Functions</TabsTrigger>
            <TabsTrigger value="columns">Columns</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="functions" className="space-y-2">
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {FORMULA_FUNCTIONS.map((func) => (
                  <div
                    key={func.name}
                    className="p-2 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => insertFunction(func)}
                  >
                    <div className="flex items-center justify-between">
                      <Badge variant="outline">{func.name}</Badge>
                      <Badge variant="secondary" className="text-xs">
                        {func.category}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {func.description}
                    </div>
                    <div className="text-xs font-mono bg-muted/30 p-1 rounded mt-1">
                      {func.example}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="columns" className="space-y-2">
            <ScrollArea className="h-48">
              <div className="grid grid-cols-2 gap-2">
                {columns.map((column) => (
                  <div
                    key={column}
                    className="p-2 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => insertColumn(column)}
                  >
                    <div className="font-medium text-sm">{column}</div>
                    <div className="text-xs text-muted-foreground">
                      {columnStats[column]?.type === 'number' ? (
                        <>Count: {columnStats[column]?.numericCount}</>
                      ) : (
                        <>Count: {columnStats[column]?.count}</>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="history" className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Recent Calculations</span>
              {history.length > 0 && (
                <Button variant="outline" size="sm" onClick={clearHistory}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {history.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    No calculations yet
                  </div>
                ) : (
                  history.map((item) => (
                    <div
                      key={item.id}
                      className="p-2 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                      onClick={() => loadFromHistory(item)}
                    >
                      <div className="font-mono text-sm">{item.formula}</div>
                      <div className="text-sm text-green-600">
                        = {typeof item.result === 'number' 
                          ? item.result.toLocaleString() 
                          : item.result}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(item.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
