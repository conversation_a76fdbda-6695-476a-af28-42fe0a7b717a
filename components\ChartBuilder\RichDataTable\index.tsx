'use client'

import { useState, useMemo, useCallback, useEffect } from 'react'
import { useVirtualizer } from '@tanstack/react-virtual'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Calculator,
  Download,
  MoreHorizontal,
  ArrowUpDown,
  Eye,
  EyeOff
} from "lucide-react"
import { cn } from "@/lib/utils"
import { TablePagination } from './TablePagination'
import { FormulaCalculator } from './FormulaCalculator'
import { RichDataTableProps, TableState, SortingState, FilterState, PaginationState } from './types'

export function RichDataTable({
  data,
  onSaveTable,
  onSaveToTable,
  maxHeight = "400px",
  enableVirtualization = true,
  enableFormulas = true,
  pageSize: initialPageSize = 50,
  showPagination = true,
  showSearch = true,
  showColumnFilters = true,
  showFormulaCalculator = true
}: RichDataTableProps) {
  // Get columns from data
  const columns = useMemo(() =>
    data.length > 0 ? Object.keys(data[0]) : [],
    [data]
  )

  // Table state
  const [tableState, setTableState] = useState<TableState>({
    pagination: {
      pageIndex: 0,
      pageSize: initialPageSize,
      totalRows: data.length,
      totalPages: Math.ceil(data.length / initialPageSize)
    },
    sorting: [],
    filters: [],
    globalFilter: '',
    selectedRows: []
  })

  const [showCalculator, setShowCalculator] = useState(false)
  const [hiddenColumns, setHiddenColumns] = useState<Set<string>>(new Set())

  // Update pagination when data changes
  useEffect(() => {
    setTableState(prev => ({
      ...prev,
      pagination: {
        ...prev.pagination,
        totalRows: data.length,
        totalPages: Math.ceil(data.length / prev.pagination.pageSize)
      }
    }))
  }, [data])

  // Format cell value for display
  const formatCellValue = useCallback((value: any): string => {
    if (value === null || value === undefined) return '-'
    if (typeof value === 'object') return JSON.stringify(value)
    if (typeof value === 'number') return value.toLocaleString()
    if (typeof value === 'boolean') return value ? 'Yes' : 'No'

    // Check if it's a base64 image and show a placeholder instead
    if (typeof value === 'string') {
      if (value.startsWith('data:image') ||
          (value.includes('base64,')) ||
          (/^[A-Za-z0-9+/=]+$/.test(value.substring(0, 20)) && value.length > 100)) {
        return '[Image Data]'
      }
    }

    return String(value)
  }, [])

  // Filter and sort data
  const processedData = useMemo(() => {
    let filtered = [...data]

    // Apply global filter
    if (tableState.globalFilter) {
      const searchTerm = tableState.globalFilter.toLowerCase()
      filtered = filtered.filter(row =>
        Object.values(row).some(value =>
          String(value).toLowerCase().includes(searchTerm)
        )
      )
    }

    // Apply column filters
    tableState.filters.forEach(filter => {
      filtered = filtered.filter(row => {
        const value = String(row[filter.column]).toLowerCase()
        const filterValue = filter.value.toLowerCase()

        switch (filter.operator) {
          case 'contains':
            return value.includes(filterValue)
          case 'equals':
            return value === filterValue
          case 'startsWith':
            return value.startsWith(filterValue)
          case 'endsWith':
            return value.endsWith(filterValue)
          default:
            return value.includes(filterValue)
        }
      })
    })

    // Apply sorting
    if (tableState.sorting.length > 0) {
      filtered.sort((a, b) => {
        for (const sort of tableState.sorting) {
          const aVal = a[sort.column]
          const bVal = b[sort.column]

          let comparison = 0
          if (typeof aVal === 'number' && typeof bVal === 'number') {
            comparison = aVal - bVal
          } else {
            comparison = String(aVal).localeCompare(String(bVal))
          }

          if (comparison !== 0) {
            return sort.direction === 'desc' ? -comparison : comparison
          }
        }
        return 0
      })
    }

    return filtered
  }, [data, tableState.globalFilter, tableState.filters, tableState.sorting])

  // Paginated data
  const paginatedData = useMemo(() => {
    if (!showPagination) return processedData

    const start = tableState.pagination.pageIndex * tableState.pagination.pageSize
    const end = start + tableState.pagination.pageSize
    return processedData.slice(start, end)
  }, [processedData, tableState.pagination, showPagination])

  // Visible columns
  const visibleColumns = useMemo(() =>
    columns.filter(col => !hiddenColumns.has(col)),
    [columns, hiddenColumns]
  )

  // Handle sorting
  const handleSort = useCallback((column: string) => {
    setTableState(prev => {
      const existingSort = prev.sorting.find(s => s.column === column)
      let newSorting: SortingState[]

      if (existingSort) {
        if (existingSort.direction === 'asc') {
          newSorting = prev.sorting.map(s =>
            s.column === column ? { ...s, direction: 'desc' as const } : s
          )
        } else {
          newSorting = prev.sorting.filter(s => s.column !== column)
        }
      } else {
        newSorting = [{ column, direction: 'asc' }, ...prev.sorting]
      }

      return { ...prev, sorting: newSorting }
    })
  }, [])

  // Handle pagination
  const handlePageChange = useCallback((pageIndex: number) => {
    setTableState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, pageIndex }
    }))
  }, [])

  const handlePageSizeChange = useCallback((pageSize: number) => {
    setTableState(prev => ({
      ...prev,
      pagination: {
        ...prev.pagination,
        pageSize,
        pageIndex: 0,
        totalPages: Math.ceil(processedData.length / pageSize)
      }
    }))
  }, [processedData.length])

  // Handle global search
  const handleGlobalFilter = useCallback((value: string) => {
    setTableState(prev => ({
      ...prev,
      globalFilter: value,
      pagination: { ...prev.pagination, pageIndex: 0 }
    }))
  }, [])

  // Toggle column visibility
  const toggleColumnVisibility = useCallback((column: string) => {
    setHiddenColumns(prev => {
      const newSet = new Set(prev)
      if (newSet.has(column)) {
        newSet.delete(column)
      } else {
        newSet.add(column)
      }
      return newSet
    })
  }, [])

  // Get sort direction for column
  const getSortDirection = useCallback((column: string) => {
    const sort = tableState.sorting.find(s => s.column === column)
    return sort?.direction
  }, [tableState.sorting])

  return (
    <div className="w-full space-y-4">
      {/* Table Controls */}
      <div className="flex items-center justify-between gap-4 p-2 bg-muted/20 border rounded-lg">
        <div className="flex items-center gap-2">
          {/* Global Search */}
          {showSearch && (
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search all columns..."
                value={tableState.globalFilter}
                onChange={(e) => handleGlobalFilter(e.target.value)}
                className="pl-8 w-64 h-9"
              />
            </div>
          )}

          {/* Column Visibility */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                <Eye className="h-4 w-4 mr-2" />
                Columns ({visibleColumns.length}/{columns.length})
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              <DropdownMenuLabel>Toggle Columns</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {columns.map((column) => (
                <DropdownMenuItem
                  key={column}
                  className="flex items-center gap-2"
                  onClick={() => toggleColumnVisibility(column)}
                >
                  <Checkbox
                    checked={!hiddenColumns.has(column)}
                    onChange={() => {}}
                  />
                  <span className="truncate">{column}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Formula Calculator Toggle */}
          {enableFormulas && showFormulaCalculator && (
            <Button
              variant={showCalculator ? "default" : "outline"}
              size="sm"
              onClick={() => setShowCalculator(!showCalculator)}
              className="h-9"
            >
              <Calculator className="h-4 w-4 mr-2" />
              Calculator
            </Button>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Row count */}
          <Badge variant="secondary">
            {processedData.length.toLocaleString()} rows
          </Badge>

          {/* Save to Dashboard */}
          {onSaveToTable && (
            <Button
              variant="outline"
              size="sm"
              onClick={onSaveToTable}
              className="h-9"
            >
              Save to Dashboard
            </Button>
          )}
        </div>
      </div>

      {/* Formula Calculator */}
      {showCalculator && enableFormulas && (
        <FormulaCalculator
          data={processedData}
          columns={visibleColumns}
          onClose={() => setShowCalculator(false)}
        />
      )}

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <div
          className="overflow-auto"
          style={{ maxHeight }}
        >
          <table className="w-full border-collapse">
            <thead className="sticky top-0 bg-background border-b z-10">
              <tr>
                {visibleColumns.map((column) => {
                  const sortDirection = getSortDirection(column)
                  return (
                    <th
                      key={column}
                      className="p-2 text-left font-medium text-xs bg-muted/50 whitespace-nowrap cursor-pointer hover:bg-muted/70 transition-colors"
                      onClick={() => handleSort(column)}
                    >
                      <div className="flex items-center gap-1">
                        <span className="truncate max-w-[200px]" title={column}>
                          {column}
                        </span>
                        <div className="flex flex-col">
                          {sortDirection === 'asc' ? (
                            <SortAsc className="h-3 w-3" />
                          ) : sortDirection === 'desc' ? (
                            <SortDesc className="h-3 w-3" />
                          ) : (
                            <ArrowUpDown className="h-3 w-3 text-muted-foreground" />
                          )}
                        </div>
                      </div>
                    </th>
                  )
                })}
              </tr>
            </thead>
            <tbody>
              {paginatedData.map((row, rowIndex) => (
                <tr
                  key={rowIndex}
                  className="border-b hover:bg-muted/30 transition-colors"
                >
                  {visibleColumns.map((column) => (
                    <td
                      key={`${rowIndex}-${column}`}
                      className="p-2 text-xs whitespace-nowrap max-w-[300px] truncate"
                      title={formatCellValue(row[column])}
                    >
                      {formatCellValue(row[column])}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {showPagination && (
          <TablePagination
            pagination={{
              ...tableState.pagination,
              totalRows: processedData.length,
              totalPages: Math.ceil(processedData.length / tableState.pagination.pageSize)
            }}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            onGoToPage={handlePageChange}
          />
        )}
      </div>
    </div>
  )
}
