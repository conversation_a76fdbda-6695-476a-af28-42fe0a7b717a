import { Dataset } from '@/types/index';
import { Saved<PERSON>hart, TableItem, PythonPlotItem } from '../DashboardSection/types';

// Cell data structure
export interface CellData {
  id: string;
  content: string;
  language: string;
  cellType?: 'code' | 'markdown';
  result?: {
    data: any[];
    output?: string;
    plots?: string[];
  };
  error?: string;
  errorDetails?: {
    message: string;
    code?: string;
    stack?: string;
    serverTrace?: string;
  };
  executionTime?: number;
  isSuccess?: boolean;
  isLoading?: boolean;
  showGraphicWalker?: boolean;
  notes?: string;
  selectedDatasetIds?: string[]; // Selected dataset IDs for each cell
}

// Query result interface
export interface QueryResult {
  [key: string]: string | number | boolean | null;
}

// Server status interface
export interface ServerStatus {
  status: 'healthy' | 'error' | 'warning';
  message: string;
}

// Execution time tracking
export interface ExecutionTimeTracking {
  startTime?: Date;
  endTime?: Date;
}

// Dataset handling hook return type
export interface UseDatasetHandlingReturn {
  datasets: Dataset[];
  datasetCache: Record<string, Dataset>;
  isLoadingDatasets: boolean;
  handleSelectDatasets: (cellId: string, datasetIds: string[]) => Promise<{ selectedList: Dataset[], datasetIds: string[] }>;
}

// Cell execution hook return type
export interface UseCellExecutionReturn {
  handleRunCell: (cellId: string, code: string, showGraphicWalker?: boolean) => Promise<void>;
  isAlasqlInitialized: boolean;
}

// Chart saving hook return type
export interface UseChartSavingReturn {
  handleSaveChart: (chartData: any, chartConfig: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => void;
  handleSaveTable: (tableData: any[], columns: string[], tableId?: string) => void;
  handleSavePlot: (plotUrl: string, plotIndex: number, plotId?: string) => void;
  handleRemoveChart: (chartId: string) => void;
  handleUpdateChart: (chartId: string, updatedProps: Partial<SavedChart>) => void;
  handleReorderCharts: (reorderedCharts: any[]) => void;
}

// Dashboard interaction hook return type
export interface UseDashboardInteractionReturn {
  handleNotebookImport: (importedNotebook: any) => void;
  handleImportChartConfig: (cellId: string, config: any) => void;
}

// Default content options
export type DefaultContentLanguage = 'sql' | 'python' | 'javascript' | 'markdown';
